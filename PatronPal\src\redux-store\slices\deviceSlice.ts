/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  createSlice,
  createAsyncThunk,
  type PayloadAction,
} from "@reduxjs/toolkit";
import apiClient from "../config";

// Device interface
interface Device {
  _id: string;
  name: string;
  active: boolean;
  userId: {
    _id: string;
    firstname: string;
    lastname: string;
    email: string;
    stripe_account_id?: string;
    stripe_acess_token?: string;
    stripe_refresh_token?: string;
    [key: string]: any;
  };
  Line1: string;
  Line2?: string;
  City: string;
  Phoneno: string;
  State: string;
  PostalCode: string;
  Country: string;
  image?: string;
  delivery?: boolean;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  businessType?: string;
  Streetaddress?: string;
  reviews?: Array<{
    _id?: string;
    food: number;
    service: number;
    ambiance: number;
    testimonial: string;
    customerId: any;
    averageScore?: number;
  }>;
  favorites?: Array<{
    _id?: string;
    customerId: any;
  }>;
}

// Request interfaces
interface DeviceCreateRequest {
  name: string;
  active: boolean;
  userId: string;
  Line1: string;
  Line2?: string;
  City: string;
  Phoneno: string;
  State: string;
  PostalCode: string;
  Country: string;
  delivery?: boolean;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  businessType?: string;
  Streetaddress?: string;
}

interface DeviceUpdateRequest extends DeviceCreateRequest {
  _id: string;
}

interface DeviceFilters {
  userId?: string;
}

interface ReviewRequest {
  deviceId: string;
  food: number;
  service: number;
  ambiance: number;
  testimonial: string;
  customerId: string;
}

interface FavoriteRequest {
  deviceId: string;
  customerId: string;
}

// State interface
interface DeviceState {
  devices: Device[];
  currentDevice: Device | null;
  favoriteDevices: Device[];
  customerReviewedDevices: Device[];
  loading: boolean;
  error: string | null;
  reviewLoading: boolean;
  favoriteLoading: boolean;
}

// Initial state
const initialState: DeviceState = {
  devices: [],
  currentDevice: null,
  favoriteDevices: [],
  customerReviewedDevices: [],
  loading: false,
  error: null,
  reviewLoading: false,
  favoriteLoading: false,
};

// Async thunks
export const getDevices = createAsyncThunk<
  Device[],
  DeviceFilters,
  { rejectValue: string }
>("device/getDevices", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.userId) params.append("userId", filters.userId);

    const response = await apiClient.get<Device[]>(
      `/device?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch devices";
    return rejectWithValue(message);
  }
});

export const getDevicesPatronpal = createAsyncThunk<
  Device[],
  DeviceFilters,
  { rejectValue: string }
>("device/getDevicesPatronpal", async (filters, { rejectWithValue }) => {
  try {
    const params = new URLSearchParams();
    if (filters.userId) params.append("userId", filters.userId);

    const response = await apiClient.get<Device[]>(
      `/Pdevice?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch devices";
    return rejectWithValue(message);
  }
});

export const getDeviceById = createAsyncThunk<
  Device,
  string,
  { rejectValue: string }
>("device/getDeviceById", async (id, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Device>(`/device/${id}`);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch device";
    return rejectWithValue(message);
  }
});

export const createDevice = createAsyncThunk<
  Device,
  { deviceData: DeviceCreateRequest; file?: File },
  { rejectValue: string }
>(
  "device/createDevice",
  async ({ deviceData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all device data to formData
      Object.entries(deviceData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Append file if provided
      if (file) {
        formData.append("image", file);
      }

      const response = await apiClient.post<Device>("/device", formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to create device";
      return rejectWithValue(message);
    }
  }
);

export const updateDevice = createAsyncThunk<
  { data: Device; message: string },
  { id: string; deviceData: DeviceUpdateRequest; file?: File },
  { rejectValue: string }
>(
  "device/updateDevice",
  async ({ id, deviceData, file }, { rejectWithValue }) => {
    try {
      const formData = new FormData();

      // Append all device data to formData
      Object.entries(deviceData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          formData.append(key, value.toString());
        }
      });

      // Append file if provided
      if (file) {
        formData.append("image", file);
      }

      const response = await apiClient.put<{
        data: Device;
        message: string;
      }>(`/device/${id}`, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });
      return response.data;
    } catch (error: any) {
      const message =
        error.response?.data?.message || "Failed to update device";
      return rejectWithValue(message);
    }
  }
);

export const deleteDevice = createAsyncThunk<
  string,
  string,
  { rejectValue: string }
>("device/deleteDevice", async (id, { rejectWithValue }) => {
  try {
    await apiClient.delete<{ message: string }>(`/device/${id}`);
    return id; // Return the deleted device ID
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to delete device";
    return rejectWithValue(message);
  }
});

export const postReview = createAsyncThunk<
  { message: string; review: any },
  ReviewRequest,
  { rejectValue: string }
>("device/postReview", async (reviewData, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{ message: string; review: any }>(
      "/device/review",
      reviewData
    );
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to post review";
    return rejectWithValue(message);
  }
});

export const getCustomerReviewedDevices = createAsyncThunk<
  Device[],
  string,
  { rejectValue: string }
>("device/getCustomerReviewedDevices", async (customerId, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Device[]>(`/devices/customer/${customerId}`);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch customer reviewed devices";
    return rejectWithValue(message);
  }
});

export const addFavorite = createAsyncThunk<
  { status: boolean; data: Device },
  FavoriteRequest,
  { rejectValue: string }
>("device/addFavorite", async ({ deviceId, customerId }, { rejectWithValue }) => {
  try {
    const response = await apiClient.post<{ status: boolean; data: Device }>(
      `/device/${deviceId}/favorite/${customerId}`
    );
    console.log('[ADD FAVOURITES]', response.data);
    
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to toggle favorite";
    return rejectWithValue(message);
  }
});

export const getFavoriteByCustomerId = createAsyncThunk<
  Device[],
  string,
  { rejectValue: string }
>("device/getFavoriteByCustomerId", async (customerId, { rejectWithValue }) => {
  try {
    const response = await apiClient.get<Device[]>(`/devices/favorites/${customerId}`);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch favorite devices";
    return rejectWithValue(message);
  }
});

// Add a new action to fetch restaurants by address
export const getDevicesByAddress = createAsyncThunk<
  Device[],
  { address?: string },
  { rejectValue: string }
>("device/getDevicesByAddress", async (_, { rejectWithValue }) => {
  try {
    // Use the existing endpoint to get all devices
    const response = await apiClient.get<Device[]>(`/Pdevice`);
    return response.data;
  } catch (error: any) {
    const message = error.response?.data?.message || "Failed to fetch restaurants";
    return rejectWithValue(message);
  }
});

// Device slice
const deviceSlice = createSlice({
  name: "device",
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    setCurrentDevice: (state, action: PayloadAction<Device>) => {
      state.currentDevice = action.payload;
    },
    clearCurrentDevice: (state) => {
      state.currentDevice = null;
    },
    clearFavoriteDevices: (state) => {
      state.favoriteDevices = [];
    },
    clearCustomerReviewedDevices: (state) => {
      state.customerReviewedDevices = [];
    },
    optimisticFavoriteUpdate: (state, action) => {
      const updatedDevice = action.payload;
      const index = state.devices.findIndex(d => d._id === updatedDevice._id);
      
      if (index !== -1) {
        state.devices[index] = updatedDevice;
      }
      
      if (state.currentDevice && state.currentDevice._id === updatedDevice._id) {
        state.currentDevice = updatedDevice;
      }
    },
  },
  extraReducers: (builder) => {
    builder
      // Get devices
      .addCase(getDevices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDevices.fulfilled, (state, action) => {
        state.loading = false;
        state.devices = action.payload;
      })
      .addCase(getDevices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch devices";
      })

      // Get devices patronpal
      .addCase(getDevicesPatronpal.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDevicesPatronpal.fulfilled, (state, action) => {
        state.loading = false;
        state.devices = action.payload;
      })
      .addCase(getDevicesPatronpal.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch devices";
      })

      // Get device by ID
      .addCase(getDeviceById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDeviceById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentDevice = action.payload;
      })
      .addCase(getDeviceById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch device";
      })

      // Create device
      .addCase(createDevice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createDevice.fulfilled, (state, action) => {
        state.loading = false;
        state.devices.push(action.payload);
      })
      .addCase(createDevice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to create device";
      })

      // Update device
      .addCase(updateDevice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateDevice.fulfilled, (state, action) => {
        state.loading = false;
        const updatedDevice = action.payload.data;
        const index = state.devices.findIndex(
          (d) => d._id === updatedDevice._id
        );
        if (index !== -1) {
          state.devices[index] = updatedDevice;
        }
        if (
          state.currentDevice &&
          state.currentDevice._id === updatedDevice._id
        ) {
          state.currentDevice = updatedDevice;
        }
      })
      .addCase(updateDevice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to update device";
      })

      // Delete device
      .addCase(deleteDevice.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(deleteDevice.fulfilled, (state, action) => {
        state.loading = false;
        state.devices = state.devices.filter((d) => d._id !== action.payload);
        if (
          state.currentDevice &&
          state.currentDevice._id === action.payload
        ) {
          state.currentDevice = null;
        }
      })
      .addCase(deleteDevice.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to delete device";
      })

      // Post review
      .addCase(postReview.pending, (state) => {
        state.reviewLoading = true;
        state.error = null;
      })
      .addCase(postReview.fulfilled, (state ) => {
        state.reviewLoading = false;
        // Optionally update the device in the state if needed
      })
      .addCase(postReview.rejected, (state, action) => {
        state.reviewLoading = false;
        state.error = action.payload || "Failed to post review";
      })

      // Get customer reviewed devices
      .addCase(getCustomerReviewedDevices.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getCustomerReviewedDevices.fulfilled, (state, action) => {
        state.loading = false;
        state.customerReviewedDevices = action.payload;
      })
      .addCase(getCustomerReviewedDevices.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch customer reviewed devices";
      })

      // Add/remove favorite
      .addCase(addFavorite.pending, (state) => {
        state.favoriteLoading = true;
        state.error = null;
      })
      .addCase(addFavorite.fulfilled, (state, action) => {
        state.favoriteLoading = false;
        const updatedDevice = action.payload.data;

        // Ensure favorites array has valid customerId values
        if (updatedDevice && updatedDevice.favorites) {
          updatedDevice.favorites = updatedDevice.favorites.filter(fav =>
            fav.customerId !== null
          );
        }

        // Update devices array
        const index = state.devices.findIndex(
          (d) => d._id === updatedDevice._id
        );
        if (index !== -1) {
          state.devices[index] = updatedDevice;
        }
        if (
          state.currentDevice &&
          state.currentDevice._id === updatedDevice._id
        ) {
          state.currentDevice = updatedDevice;
        }

        // Update favoriteDevices array for immediate navbar updates
        const favoriteIndex = state.favoriteDevices.findIndex(
          (d) => d._id === updatedDevice._id
        );

        // Check if device has any favorites (is favorited by at least one user)
        const hasFavorites = updatedDevice.favorites && updatedDevice.favorites.length > 0;

        if (hasFavorites && favoriteIndex === -1) {
          // Device was favorited and not in favorites list - add it
          state.favoriteDevices.push(updatedDevice);
        } else if (hasFavorites && favoriteIndex !== -1) {
          // Device was favorited and already in favorites list - update it
          state.favoriteDevices[favoriteIndex] = updatedDevice;
        } else if (!hasFavorites && favoriteIndex !== -1) {
          // Device was unfavorited - remove it from favorites list
          state.favoriteDevices.splice(favoriteIndex, 1);
        }
      })
      .addCase(addFavorite.rejected, (state, action) => {
        state.favoriteLoading = false;
        state.error = action.payload || "Failed to toggle favorite";
      })

      // Get favorite devices by customer ID
      .addCase(getFavoriteByCustomerId.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getFavoriteByCustomerId.fulfilled, (state, action) => {
        state.loading = false;
        state.favoriteDevices = action.payload;
      })
      .addCase(getFavoriteByCustomerId.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch favorite devices";
      })

      // Get devices by address
      .addCase(getDevicesByAddress.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(getDevicesByAddress.fulfilled, (state, action) => {
        state.loading = false;
        state.devices = action.payload;
      })
      .addCase(getDevicesByAddress.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload || "Failed to fetch nearby restaurants";
      });
  },
});

// Export actions
export const {
  clearError,
  setCurrentDevice,
  clearCurrentDevice,
  clearFavoriteDevices,
  clearCustomerReviewedDevices,
  optimisticFavoriteUpdate,
} = deviceSlice.actions;

// Selectors
export const selectDevices = (state: { device: DeviceState }) =>
  state.device.devices;
export const selectCurrentDevice = (state: { device: DeviceState }) =>
  state.device.currentDevice;
export const selectFavoriteDevices = (state: { device: DeviceState }) =>
  state.device.favoriteDevices;
export const selectCustomerReviewedDevices = (state: { device: DeviceState }) =>
  state.device.customerReviewedDevices;
export const selectDeviceLoading = (state: { device: DeviceState }) =>
  state.device.loading;
export const selectDeviceError = (state: { device: DeviceState }) =>
  state.device.error;
export const selectReviewLoading = (state: { device: DeviceState }) =>
  state.device.reviewLoading;
export const selectFavoriteLoading = (state: { device: DeviceState }) =>
  state.device.favoriteLoading;

// New selector to get user's favorite devices from all devices
export const selectUserFavoriteDevices = (userId: string) => (state: { device: DeviceState }) => {
  if (!userId) return [];

  return state.device.devices.filter(device => {
    if (!device.favorites || device.favorites.length === 0) return false;

    return device.favorites.some(fav => {
      // Handle both object and string customerId formats
      if (typeof fav.customerId === 'object' && fav.customerId?._id) {
        return fav.customerId._id === userId;
      }
      return fav.customerId === userId;
    });
  });
};

// Export reducer
export default deviceSlice.reducer;