/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState, useEffect, useRef } from 'react';
import { X } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import {
  getModifiers,
  selectModifierLoading,
  selectModifiers,
} from '../../redux-store/slices/modifierSlice';
import type { AppDispatch } from '../../redux-store/store';

interface ModifierModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId?: string;
  userId?: string;
  onAddToOrder: (selectedModifiers: any) => void;
  productName?: string;
  basePrice?: number;
}

const ModifierModal: React.FC<ModifierModalProps> = ({ 
  isOpen, 
  onClose, 
  productId,
  userId,
  onAddToOrder,
  productName = "Product", // This should be passed from MenuItems
  basePrice = 0
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [selectedModifiers, setSelectedModifiers] = useState<any>({});
  const [totalPrice, setTotalPrice] = useState<number>(basePrice);
  const [error, setError] = useState<string | null>(null);
  const [note, setNote] = useState<string>('');
  const [discountType, setDiscountType] = useState<string>('');
  const [discountAmount, setDiscountAmount] = useState<string>('');
  const fetchedRef = useRef(false);
  
  const modifiers = useSelector(selectModifiers);
  const loading = useSelector(selectModifierLoading);

  // Filter modifiers for the current product
  const productModifiers = modifiers.filter(modifier => 
    modifier.productId && 
    (typeof modifier.productId === 'string' 
      ? modifier.productId === productId 
      : modifier.productId._id === productId)
  );

  useEffect(() => {
    // Reset selected modifiers when modal opens
    if (isOpen) {
      setSelectedModifiers({});
      setTotalPrice(basePrice);
      setNote('');
      setDiscountType('');
      setDiscountAmount('');
    }
  }, [isOpen, basePrice]);

  useEffect(() => {
    if (isOpen && productId && userId && !fetchedRef.current) {
      setError(null);
      console.log(`Fetching modifiers for product: ${productId}, userId: ${userId}`);
      fetchedRef.current = true;
      
      dispatch(getModifiers({ userId }))
        .unwrap()
        .catch((err) => {
          console.error("Error fetching modifiers:", err);
          setError(err || "Failed to load modifiers");
        });
    }
    
    if (!isOpen) {
      fetchedRef.current = false;
    }
  }, [isOpen, productId, userId, dispatch]);

  const handleModifierSelection = (modifierName: string, propertyName: string, price: number) => {
    setSelectedModifiers((prev: any) => {
      const newModifiers = {
        ...prev,
        [modifierName]: {
          name: propertyName,
          price: price || 0 // Ensure price is always a number
        }
      };
      
      // Calculate total price including base price and all modifiers
      setTimeout(() => {
        let total = basePrice;
        Object.values(newModifiers).forEach((modifier: any) => {
          if (modifier && typeof modifier.price === 'number') {
            total += modifier.price;
          }
        });
        setTotalPrice(total);
      }, 0);
      
      return newModifiers;
    });
  };

  const handleAddToOrder = () => {
    // Create order data with all fields optional and ensure proper types
    const orderData = {
      ...selectedModifiers,
      note: note || '',
      totalPrice: totalPrice // Include the calculated total price
    };
    
    // Call the onAddToOrder callback with the order data
    onAddToOrder(orderData);
    onClose();
  };

  const getSelectedModifierValue = (modifierName: string) => {
    return selectedModifiers[modifierName]?.name || '';
  };

  const formatPrice = (price: number) => {
    return price === 0 ? 'Free' : `+$${price.toFixed(0)}`;
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 backdrop-blur flex items-center justify-center z-50 px-4" style={{ backgroundColor: "rgba(0, 0, 0, 0.4)" }}>
      <div className="bg-white rounded-2xl w-full max-w-md max-h-[95vh] overflow-hidden flex flex-col">
        {/* Header */}
        <div className="p-6 pb-4 flex justify-between items-center border-b border-gray-100">
          <h2 className="text-xl font-bold text-gray-900">{productName}</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors p-1 cursor-pointer"
          >
            <X size={24} />
          </button>
        </div>
        
        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6 space-y-8">
            {/* Discount Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Discount</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-gray-600 mb-2">Select Discount Type</label>
                  <select 
                    value={discountType}
                    onChange={(e) => setDiscountType(e.target.value)}
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  >
                    <option value="">Select Type (Optional)</option>
                    <option value="percentage">Percentage</option>
                    <option value="fixed">Fixed Amount</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm text-gray-600 mb-2">Discount Amount</label>
                  <input
                    type="text"
                    value={discountAmount}
                    onChange={(e) => setDiscountAmount(e.target.value)}
                    placeholder="Enter Amount"
                    className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              </div>
            </div>

            {error ? (
              <div className="text-red-500 p-4 text-center bg-red-50 rounded-xl">
                <p>{error}</p>
                <button 
                  onClick={() => {
                    if (userId && productId) {
                      fetchedRef.current = false;
                      dispatch(getModifiers({ userId }));
                    }
                  }}
                  className="mt-2 px-4 py-2 bg-orange-500 text-white rounded-xl hover:bg-orange-600 transition-colors cursor-pointer"
                >
                  Retry
                </button>
              </div>
            ) : loading ? (
              <div className="flex justify-center items-center p-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              </div>
            ) : productModifiers.length === 0 ? (
              <div className="text-center p-8 text-gray-500 bg-gray-50 rounded-xl">
                No customization options available
              </div>
            ) : (
              <div className="space-y-8">
                {productModifiers.map((modifier) => (
                  <div key={modifier._id}>
                    {modifier.Modifier && modifier.Modifier.map((mod: any) => (
                      <div key={mod._id} className="space-y-4">
                        <h3 className="text-lg font-semibold text-gray-900">{mod.name}</h3>
                        <div className="grid grid-cols-3 gap-3">
                          {mod.properties && mod.properties.map((prop: any) => {
                            const isSelected = getSelectedModifierValue(mod.name) === prop.name;
                            return (
                              <button
                                key={prop._id}
                                onClick={() => handleModifierSelection(mod.name, prop.name, prop.price)}
                                className={`w-full px-4 py-2 rounded-3xl border transition-all text-left gap-1 flex justify-between items-center cursor-pointer ${
                                  isSelected
                                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                                    : 'border-gray-200 bg-white text-gray-700 hover:border-gray-300'
                                }`}
                              >
                                <span className="font-medium">{prop.name}</span>
                                <span className={`text-sm font-medium ${
                                  isSelected ? 'text-orange-600' : 'text-gray-700'
                                }`}>
                                  {formatPrice(prop.price)}
                                </span>
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                ))}
              </div>
            )}

            {/* Add Note Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Add note</h3>
              <textarea
                value={note}
                onChange={(e) => setNote(e.target.value)}
                placeholder="Type your note here.."
                rows={4}
                className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
              />
            </div>

            {/* Availability Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">In case this item is not available</h3>
              <select className="w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent">
                <option>Cancel the whole order</option>
                <option>Remove this item only</option>
                <option>Call me</option>
              </select>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 pt-4 border-t border-gray-100 flex justify-between items-center bg-white">
          <div>
            <p className="text-sm text-gray-500 mb-1">Total</p>
            <p className="text-2xl font-bold text-gray-900">${totalPrice.toFixed(2)}</p>
          </div>
          <button
            onClick={handleAddToOrder}
            className="bg-orange-500 hover:bg-orange-600 text-white px-8 py-4 rounded-2xl font-semibold text-lg transition-colors min-w-[140px] cursor-pointer"
          >
            Add to order
          </button>
        </div>
      </div>
    </div>
  );
};

export default ModifierModal;
