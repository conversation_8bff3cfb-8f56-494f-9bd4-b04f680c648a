import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';

interface TabOption {
  id: string;
  label: string;
  isComingSoon?: boolean;
}

interface DeliveryTabsProps {
  onTabChange?: (tabId: string) => void;
}

const Topbar: React.FC<DeliveryTabsProps> = ({ onTabChange }) => {
  const navigate = useNavigate();

  
  const tabs: TabOption[] = [
    { id: 'delivery', label: 'Delivery' },
    { id: 'pickup', label: 'Pick Up' },
    { id: 'stores', label: 'Stores' },
    { id: 'comingSoon', label: 'Coming Soon', isComingSoon: true },
  ];

  const [activeTab, setActiveTab] = useState<string>(tabs[0].id);

  const handleTabClick = (tabId: string) => {
    setActiveTab(tabId);
    if (onTabChange) {
      onTabChange(tabId);
    }
  };

  return (
    <div className="w-full md:border-b border-none border-gray-200">

      <div className="flex items-center space-x-2">
        {/* Back button - hidden on mobile, visible on tablet and above */}
        <button
          onClick={() => navigate(-1)}
          className="hidden md:flex items-center justify-center w-8 h-8 text-gray-600 hover:text-black hover:bg-gray-100 rounded-full transition-colors"
          title="Go back"
        >
          <ArrowLeft className="w-4 h-4" />
        </button>
        {tabs.map((tab) => (
          <div
            key={tab.id}
            onClick={() => !tab.isComingSoon && handleTabClick(tab.id)}
            className={`relative px-4 py-3 cursor-pointer ${
              tab.isComingSoon ? 'cursor-default' : ''
            }`}
          >
            {tab.isComingSoon ? (
              <span className="px-4 py-1 text-sm text-primary border border-primary rounded-full">
                {tab.label}
              </span>
            ) : (
              <>
                <span
                  className={`text-sm font-medium ${
                    activeTab === tab.id ? 'text-black' : 'text-gray-500'
                  }`}
                >
                  {tab.label}
                </span>
                {activeTab === tab.id && (
                  <div className="absolute bottom-0 left-0 w-full h-0.5 bg-primary"></div>
                )}
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Topbar
