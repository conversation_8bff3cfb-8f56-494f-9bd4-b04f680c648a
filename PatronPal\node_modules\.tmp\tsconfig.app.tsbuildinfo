{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/assets/assets.ts", "../../src/components/addressdropdown.tsx", "../../src/components/breadcrumb.tsx", "../../src/components/cartsummary.tsx", "../../src/components/checkoutform.tsx", "../../src/components/creditcardform.tsx", "../../src/components/deliveryaddresspopup.tsx", "../../src/components/favoritespage.tsx", "../../src/components/footer.tsx", "../../src/components/navbar.tsx", "../../src/components/topbar.tsx", "../../src/components/modal/allreviwsmodal.tsx", "../../src/components/modal/gallerymodal.tsx", "../../src/components/modal/infomodal.tsx", "../../src/components/modal/mapaddressmodal.tsx", "../../src/components/modal/modifiermodal.tsx", "../../src/components/modal/reviewmodal.tsx", "../../src/components/productpagecomponent/menuitems.tsx", "../../src/components/productpagecomponent/topsection.tsx", "../../src/components/productpagecomponent/youritems.tsx", "../../src/components/restaurentlistbyfiltercomponent/cuisinecard.tsx", "../../src/components/restaurentlistbyfiltercomponent/dailydeals.tsx", "../../src/components/restaurentlistbyfiltercomponent/filtersidebar.tsx", "../../src/components/restaurentlistbyfiltercomponent/horizontalrestaurantlist.tsx", "../../src/components/restaurentlistbyfiltercomponent/restaurantcard.tsx", "../../src/components/restaurentlistbyfiltercomponent/restaurantgrid.tsx", "../../src/components/restaurentlistbyfiltercomponent/searchresults.tsx", "../../src/components/crisp/usecrisp.ts", "../../src/components/profile/profilesettings.tsx", "../../src/hooks/usetokenvalidation.tsx", "../../src/pages/aboutus.tsx", "../../src/pages/cartpage.tsx", "../../src/pages/checkout.tsx", "../../src/pages/contactus.tsx", "../../src/pages/home.tsx", "../../src/pages/notfound.tsx", "../../src/pages/orderhistorypage.tsx", "../../src/pages/rewards.tsx", "../../src/pages/product/productpage.tsx", "../../src/pages/restaurant/restaurantlist.tsx", "../../src/pages/restaurant/restaurantlistbyfilters.tsx", "../../src/pages/restaurant/restaurentlistnavbar.tsx", "../../src/pages/restaurant/hooks/userestaurantdata.ts", "../../src/pages/restaurant/hooks/userestaurantfilters.ts", "../../src/pages/restaurant/types/types.ts", "../../src/pages/tracking/livetrackingpage.tsx", "../../src/pages/tracking/trackingpage.tsx", "../../src/pages/users/forgetpassword.tsx", "../../src/pages/users/loginpage.tsx", "../../src/pages/users/otpverificationpage.tsx", "../../src/pages/users/resetpassword.tsx", "../../src/pages/users/signuppage.tsx", "../../src/pages/users/global.d.ts", "../../src/pages/profile/profilesettings.tsx", "../../src/redux-store/api.types.ts", "../../src/redux-store/config.ts", "../../src/redux-store/hooks.ts", "../../src/redux-store/store.ts", "../../src/redux-store/slices/addressslice.ts", "../../src/redux-store/slices/cartslice.ts", "../../src/redux-store/slices/categoryslice.ts", "../../src/redux-store/slices/customerslice.ts", "../../src/redux-store/slices/deviceslice.ts", "../../src/redux-store/slices/menuslice.ts", "../../src/redux-store/slices/modifierslice.ts", "../../src/redux-store/slices/ordertrackingslice.ts", "../../src/redux-store/slices/parentonlineorderslice.ts", "../../src/redux-store/slices/productslice.ts", "../../src/redux-store/slices/reviewslice.ts", "../../src/redux-store/slices/subonlineorderitemslice.ts", "../../src/utils/protectedroute.tsx", "../../src/utils/googleaccountshelper.ts", "../../src/utils/paymentapi.ts", "../../src/utils/restaurantutils.ts", "../../src/utils/stripe.ts", "../../src/utils/taxutils.ts"], "version": "5.8.3"}