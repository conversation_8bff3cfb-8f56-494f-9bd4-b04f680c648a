// ProductList.tsx resturentCard - Updated to match resturentList design
import React, { useEffect } from 'react';

import { Star, MapPin, Clock, Heart, Phone as PhoneIcon } from 'lucide-react';
import { handleRestaurantTaxData } from '../../utils/taxUtils';

interface Restaurant {
  _id: string;
  name: string;
  businessType?: string;
  image?: string;
  Line1: string;
  Line2?: string;
  City: string;
  State: string;
  Phoneno: string;
  PostalCode: string;
  Country: string;
  active: boolean | string;
  userId: any;
  delivery?: boolean | string;
  deliveryStartTime?: string;
  deliveryEndTime?: string;
  ChargesperKm?: number;
  ChargesFreeKm?: number;
  pickupStartTime?: string;
  pickupEndTime?: string;
  reviews?: Array<{
    _id?: string;
    food: number;
    service: number;
    ambiance: number;
    testimonial: string;
    customerId: any;
    averageScore?: number;
  }>;
  favorites?: Array<{
    _id?: string;
    customerId: any;
  }>;
}

interface RestaurantCardProps {
  restaurant: Restaurant;
  userId?: string;
  onFavoriteToggle?: (restaurantId: string) => void;
  favoriteLoading?: boolean;
  serviceType?: string;
  onRestaurantClick?: (restaurantId: string) => void;
}

const RestaurantCard: React.FC<RestaurantCardProps> = ({
  restaurant,
  userId,
  onFavoriteToggle,
  serviceType = 'all',
  onRestaurantClick
}) => {
  useEffect(() => {
    console.log('[Restaurant]', restaurant)
  }, [])

  // Calculate average rating from reviews
  const calculateAverageRating = () => {
    if (!restaurant.reviews || restaurant.reviews.length === 0) {
      return null;
    }

    const totalReviews = restaurant.reviews.length;
    const averageFood = restaurant.reviews.reduce((sum, review) => sum + review.food, 0) / totalReviews;
    const averageService = restaurant.reviews.reduce((sum, review) => sum + review.service, 0) / totalReviews;
    const averageAmbiance = restaurant.reviews.reduce((sum, review) => sum + review.ambiance, 0) / totalReviews;

    const overallAverage = (averageFood + averageService + averageAmbiance) / 3;
    return Number(overallAverage.toFixed(1));
  };

  const formatReviewCount = () => {
    if (!restaurant.reviews || restaurant.reviews.length === 0) return null;
    return `${restaurant.reviews.length}`;
  };

  const isRestaurantFavorited = () => {

    if (!restaurant.favorites || restaurant.favorites.length === 0 || !userId) {
      console.log('[RestaurantCard] No favorites or userId, returning false', {
        hasFavorites: !!restaurant.favorites,
        favoritesLength: restaurant.favorites?.length,
        hasUserId: !!userId,
        restaurantId: restaurant._id
      });
      return false;
    }

    const result = restaurant.favorites.some(fav => {
      const favCustomerId = typeof fav.customerId === 'object' && fav.customerId?._id
        ? fav.customerId._id
        : fav.customerId;
      const isMatch = favCustomerId === userId;
      console.log('[RestaurantCard] Checking favorite:', {
        favCustomerId,
        userId,
        isMatch,
        restaurantId: restaurant._id
      });
      return isMatch;
    });

    console.log('[RestaurantCard] Final favorite result:', {
      result,
      restaurantId: restaurant._id,
      restaurantName: restaurant.name
    });
    return result;
  };

  const generatePromoText = () => {
    if (restaurant.ChargesFreeKm && restaurant.ChargesFreeKm > 0) {
      return `${restaurant.ChargesFreeKm} KM Free`;
    }
    if (restaurant.ChargesperKm && restaurant.ChargesperKm < 2) {
      return "Low Charges";
    }
    return null;
  };


  const rating = calculateAverageRating();
  const reviewCount = formatReviewCount();
  const isFavorited = isRestaurantFavorited();
  const promoText = generatePromoText();
  // const { discount, code } = generateDiscountInfo();

  // Add useEffect to track changes in restaurant favorites
  useEffect(() => {
    console.log('[RestaurantCard] Restaurant data changed:', {
      restaurantId: restaurant._id,
      restaurantName: restaurant.name,
      favoritesCount: restaurant.favorites?.length || 0,
      isFavorited,
      userId,
      favorites: restaurant.favorites
    });
  }, [restaurant.favorites, userId, isFavorited, restaurant._id, restaurant.name]);

  const isOpen = () => {
    if (serviceType.toLowerCase() === 'delivery') {
      if (!restaurant.deliveryStartTime || !restaurant.deliveryEndTime) return true;
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(restaurant.deliveryStartTime.replace(':', ''));
      const endTime = parseInt(restaurant.deliveryEndTime.replace(':', ''));
      return currentTime >= startTime && currentTime <= endTime;
    } else if (serviceType.toLowerCase() === 'pickup') {
      if (!restaurant.pickupStartTime || !restaurant.pickupEndTime) return true;
      const now = new Date();
      const currentTime = now.getHours() * 100 + now.getMinutes();
      const startTime = parseInt(restaurant.pickupStartTime.replace(':', ''));
      const endTime = parseInt(restaurant.pickupEndTime.replace(':', ''));
      return currentTime >= startTime && currentTime <= endTime;
    }
    return true;
  };

  const formatAddress = () => {
    const parts = [restaurant.Country, restaurant.City];
    const filteredParts = parts.filter(
      part =>
        part &&
        part.trim().toLowerCase() !== 'undefined' &&
        part.trim() !== ''
    );
    return filteredParts.join(', ');
  };

  function isValidTime(time?: string | null): boolean {
    return (
      typeof time === 'string' &&
      time.trim() !== '' &&
      time.trim().toLowerCase() !== 'undefined' &&
      time.trim().toLowerCase() !== 'null'
    );
  }

  // ✅ 24-hour → 12-hour formatter
  function formatTime12Hour(timeStr: string): string {
    const [hours, minutes] = timeStr.split(':');
    const date = new Date();
    date.setHours(parseInt(hours));
    date.setMinutes(parseInt(minutes));

    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true,
    });
  }


  const getServiceTimes = () => {
    if (serviceType.toLowerCase() === 'delivery') {
      return {
        startTime: restaurant.deliveryStartTime,
        endTime: restaurant.deliveryEndTime
      };
    } else if (serviceType.toLowerCase() === 'pickup') {
      return {
        startTime: restaurant.pickupStartTime,
        endTime: restaurant.pickupEndTime
      };
    }
    return {
      startTime: restaurant.deliveryStartTime,
      endTime: restaurant.deliveryEndTime
    };
  };

  const { startTime, endTime } = getServiceTimes();

  const handleCardClick = async (e: React.MouseEvent) => {
    // Don't navigate if clicking on the favorite button
    if ((e.target as HTMLElement).closest('button')) {
      return;
    }

    // Store userId and fetch tax data using utility function
    const restaurantUserId = restaurant?.userId?._id || restaurant?.userId;
    if (restaurantUserId) {
      try {
        await handleRestaurantTaxData(restaurantUserId);
      } catch (error) {
        console.error('Error handling restaurant tax data:', error);
      }
    }

    if (onRestaurantClick) {
      onRestaurantClick(restaurant._id);
    } else {
      // Default navigation if onRestaurantClick is not provided
      window.location.href = `/product/${restaurant._id}/${restaurant?.userId?._id || restaurant?.userId}`;
    }
  };

  const handleFavoriteClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onFavoriteToggle) {
      onFavoriteToggle(restaurant._id);
    } else {
      console.log('[RestaurantCard] onFavoriteToggle is not defined');
    }
  };


  return (
    <div
      className="relative rounded-lg overflow-hidden bg-white p-1 shadow-sm hover:shadow-md transition-shadow cursor-pointer w-full h-full"
      onClick={handleCardClick}
    >
      <div className="relative">
        <img
          src={restaurant.image || 'https://patronpal.com/assets/aaa.jpeg'}
          alt={restaurant.name}
          className="w-full h-40 object-cover rounded-lg"
        />
        <div className="absolute inset-0 bg-black/20 bg-opacity-50 rounded-lg"></div>

        {/* Promo Text */}
        {promoText && (
          <div className="absolute top-4 left-4 bg-orange-500 text-white text-xs px-2 py-1 rounded-xl">
            {promoText}
          </div>)}

        {/* Discount Info */}
        {/* <div className="absolute top-12 left-4 bg-orange-500 text-white text-xs px-2 py-1 rounded-xl">
      <span className="font-bold">{discount}</span> {code}
    </div> */}

        {/* Favorite Button */}
        {onFavoriteToggle && (
          <button
            onClick={handleFavoriteClick}
            className="absolute top-4 cursor-pointer right-4 bg-white p-2 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
          >
            <Heart
              className={`h-5 w-5 transition-all duration-200 ${
                isFavorited
                  ? 'text-orange-500 fill-current scale-110'
                  : 'text-gray-400 hover:text-orange-500'
              }`}
            />
          </button>
        )}

        {/* Status Indicator */}
        {!isOpen() && (
          <div className="absolute bottom-4 left-4 bg-red-500 text-white text-xs px-2 py-1 rounded-xl">
            Closed
          </div>
        )}

        {/* Active Status */}
        {!restaurant.active && (
          <div className="absolute bottom-4 right-4 bg-gray-500 text-white text-xs px-2 py-1 rounded-xl">
            Inactive
          </div>
        )}
      </div>

      <div className="pt-2 pb-0 p-1">
        <div className="flex items-start justify-between gap-2">
          {/* LEFT: Restaurant Details */}
          <div className="flex-1 min-w-0 max-w-[calc(100%-0px)]">
            <h3 className="block text-md font-semibold text-gray-900 truncate hover:text-orange-500 transition-colors">
              {restaurant.name}
            </h3>

            <p className="text-gray-600 text-sm truncate">
              {restaurant.businessType || 'Restaurant'}
            </p>

            <div className='flex flex-col space-y-0.5 py-1 items-start'>
              {/* Location */}
              <div className="flex items-center mt-1 space-x-1 text-gray-500 min-w-0">
                <MapPin className="h-4 w-4 shrink-0 flex-none" />
                <span className="truncate text-xs">
                  {formatAddress()}
                </span>
              </div>

              {/* Phone */}
              {restaurant.Phoneno && restaurant.Phoneno !== 'null' && restaurant.Phoneno !== 'undefined' && (
                <div className="flex items-center mt-1 text-xs text-gray-500 min-w-0">
                  <PhoneIcon className="h-3 w-3 mr-1 shrink-0" />
                  <span className="truncate">
                    {restaurant.Phoneno}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* RIGHT: Rating */}
          {rating !== null && reviewCount && (
            <div className="flex items-center space-x-1 shrink-0 pt-1">
              <Star className="h-3 w-3 text-yellow-400 fill-current" />
              <span className="text-xs font-medium whitespace-nowrap">{rating}</span>
              <span className="bg-orange-500 text-white text-xs px-1 rounded-xl whitespace-nowrap">
                {reviewCount}
              </span>
            </div>
          )}
        </div>

        <div className='flex justify-between items-center w-full mt-2'>
          {/* Service Time Info */}
          {isValidTime(startTime) && isValidTime(endTime) && (
            <div className="flex items-center text-xs text-gray-500 min-w-0">
              <Clock className="h-4 w-4 mr-1 shrink-0 flex-none" />
              <span className="truncate">
                {formatTime12Hour(startTime!)} - {formatTime12Hour(endTime!)}
              </span>
            </div>
          )}

          {/* Delivery Charges - only show for delivery */}
          {serviceType.toLowerCase() === 'delivery' && restaurant.ChargesperKm !== 0 && restaurant.ChargesperKm && (
            <div className="flex items-center text-xs text-gray-500 shrink-0">
              <span className="whitespace-nowrap">${restaurant.ChargesperKm}/km delivery</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RestaurantCard;
