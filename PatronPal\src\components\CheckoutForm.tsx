import React, { useState, useEffect } from 'react';
import { MapPin } from 'lucide-react';
import { Elements } from '@stripe/react-stripe-js';
import DeliveryAddressPopup from './DeliveryAddressPopup';
import CreditCardForm from './CreditCardForm';
import stripePromise from '../utils/stripe';
import { getCurrentRestaurantStripeAccount, getStoredRestaurantUserId } from '../utils/restaurantUtils';
import Swal from 'sweetalert2';

interface CheckoutFormProps {
    currentCustomer: any;
    orderTypes: string;
    selectedMethod: string;
    setSelectedMethod: (method: string) => void;
    handleSubmit: () => void;
    isOrderLoading: boolean;
    cartTotal: number;
    onPaymentSuccess?: (paymentIntentId: string, chargeId: string) => void;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({
    currentCustomer,
    orderTypes,
    selectedMethod,
    setSelectedMethod,
    handleSubmit,
    isOrderLoading,
    cartTotal,
    onPaymentSuccess
}) => {
    // const [firstName, setFirstName] = useState(currentCustomer?.fname);
    // const [lastName, setLastName] = useState(currentCustomer?.lname);
    // const [mobileNumber, setMobileNumber] = useState(currentCustomer?.Phone);
    // const [email, setEmail] = useState(currentCustomer?.Email);
    // const [isEditing, setIsEditing] = useState(false);
    const [voucherCode, setVoucherCode] = useState('');

    // Delivery address state
    const [deliveryAddress, setDeliveryAddress] = useState('');
    const [isAddressPopupOpen, setIsAddressPopupOpen] = useState(false);

    // Stripe account state
    const [stripeAccountId, setStripeAccountId] = useState<string>('');
    const [isLoadingStripeAccount, setIsLoadingStripeAccount] = useState(false);

    const loyaltyPoints = 3200;
    const loyaltyValue = 50;

    // Initialize delivery address from Address2 if it exists
    useEffect(() => {
        if (currentCustomer?.Address2) {
            const { street, city, state, zipcode } = currentCustomer.Address2;
            if (street || city || state || zipcode) {
                const formattedAddress = `${street || ''}, ${city || ''}, ${state || ''} ${zipcode || ''}`.replace(/,\s*,/g, ',').replace(/^\s*,\s*|\s*,\s*$/g, '');
                setDeliveryAddress(formattedAddress);
            }
        }
    }, [currentCustomer]);

    // Fetch stripe account ID when component mounts
    useEffect(() => {
        const fetchStripeAccount = async () => {
            setIsLoadingStripeAccount(true);
            try {
                const userId = getStoredRestaurantUserId();
                if (userId) {
                    const stripeAccount = await getCurrentRestaurantStripeAccount(userId);
                    if (stripeAccount) {
                        setStripeAccountId(stripeAccount);
                        console.log('Stripe account ID set:', stripeAccount);
                    } else {
                        console.error('No stripe account found for restaurant');
                        Swal.fire({
                            title: 'Payment Setup Required',
                            text: 'This restaurant has not set up payment processing yet.',
                            icon: 'warning',
                            confirmButtonText: 'OK',
                            confirmButtonColor: '#FF5C00',
                        });
                    }
                } else {
                    console.error('No restaurant userId found in localStorage');
                }
            } catch (error) {
                console.error('Error fetching stripe account:', error);
            } finally {
                setIsLoadingStripeAccount(false);
            }
        };

        fetchStripeAccount();
    }, []);

    const applyVoucher = () => {
        if (voucherCode.trim()) {
            alert(`Voucher "${voucherCode}" applied!`);
        }
    };

    const applyLoyaltyPoints = () => {
        alert(`Applied ${loyaltyPoints} loyalty points worth $${loyaltyValue}`);
    };

    const paymentMethods = [
        {
            id: 'googlepay',
            name: 'GooglePay',
            icon: (
                <div className="flex items-center justify-center w-8 h-8">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <g clipPath="url(#clip0_7385_21895)">
                            <path fillRule="evenodd" clipRule="evenodd" d="M5.17192 12.0001C5.17192 11.2206 5.3013 10.4733 5.53248 9.77239L1.48811 6.68408C0.699859 8.28439 0.255859 10.0878 0.255859 12.0001C0.255859 13.9109 0.699484 15.713 1.48642 17.3123L5.52855 14.218C5.29961 13.5203 5.17192 12.7758 5.17192 12.0001Z" fill="#FBBC05" />
                            <path fillRule="evenodd" clipRule="evenodd" d="M12.2731 4.90912C13.9664 4.90912 15.4958 5.50912 16.6975 6.49088L20.1933 3C18.0631 1.14544 15.332 0 12.2731 0C7.52409 0 3.44259 2.71575 1.48828 6.684L5.53247 9.77231C6.46434 6.94369 9.12066 4.90912 12.2731 4.90912Z" fill="#EA4335" />
                            <path fillRule="evenodd" clipRule="evenodd" d="M12.2731 19.0907C9.12084 19.0907 6.46453 17.0562 5.53266 14.2275L1.48828 17.3153C3.44259 21.2841 7.52409 23.9999 12.2731 23.9999C15.2041 23.9999 18.0025 22.959 20.1027 21.009L16.2638 18.0413C15.1807 18.7236 13.8166 19.0907 12.2731 19.0907Z" fill="#34A853" />
                            <path fillRule="evenodd" clipRule="evenodd" d="M23.7441 12.0001C23.7441 11.291 23.6348 10.5273 23.4709 9.81836H12.2734V14.4547H18.7189C18.3966 16.0355 17.5195 17.2507 16.2642 18.0415L20.1031 21.0093C22.3092 18.9618 23.7441 15.9115 23.7441 12.0001Z" fill="#4285F4" />
                        </g>
                        <defs>
                            <clipPath id="clip0_7385_21895">
                                <rect width="24" height="24" fill="white" />
                            </clipPath>
                        </defs>
                    </svg>
                </div>
            )
        },
        {
            id: 'creditcard',
            name: 'Credit Card',
            icon: (
                <div className="flex items-center justify-center w-8 h-8">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 13.3599H2C1.59 13.3599 1.25 13.0199 1.25 12.6099C1.25 12.1999 1.59 11.8599 2 11.8599H19C19.41 11.8599 19.75 12.1999 19.75 12.6099C19.75 13.0199 19.41 13.3599 19 13.3599Z" fill="#292D32" />
                        <path d="M15.22 21.7498H5.78003C2.35003 21.7498 1.25 20.6598 1.25 17.2698V10.2798C1.25 7.5898 1.86002 5.99981 4.96002 5.81981C5.22002 5.80981 5.49003 5.7998 5.78003 5.7998H15.22C18.65 5.7998 19.75 6.8898 19.75 10.2798V17.4298C19.71 20.6998 18.61 21.7498 15.22 21.7498ZM5.78003 7.2998C5.51003 7.2998 5.26003 7.30981 5.03003 7.31981C3.24003 7.42981 2.75 7.8098 2.75 10.2798V17.2698C2.75 19.8298 3.17003 20.2498 5.78003 20.2498H15.22C17.8 20.2498 18.22 19.8498 18.25 17.4198V10.2798C18.25 7.7198 17.83 7.2998 15.22 7.2998H5.78003Z" fill="#292D32" />
                        <path d="M19 18.18C18.81 18.18 18.62 18.11 18.49 17.98C18.34 17.84 18.25 17.64 18.25 17.43V10.28C18.25 7.72 17.83 7.3 15.22 7.3H5.78003C5.51003 7.3 5.26003 7.31 5.03003 7.32C4.83003 7.33 4.62998 7.25 4.47998 7.11C4.32998 6.97 4.25 6.77 4.25 6.56C4.29 3.3 5.39003 2.25 8.78003 2.25H18.22C21.65 2.25 22.75 3.34 22.75 6.73V13.72C22.75 16.41 22.14 18 19.04 18.18C19.03 18.18 19.01 18.18 19 18.18ZM5.78003 5.8H15.22C18.65 5.8 19.75 6.89 19.75 10.28V16.6C20.91 16.39 21.25 15.79 21.25 13.72V6.73C21.25 4.17 20.83 3.75 18.22 3.75H8.78003C6.50003 3.75 5.91003 4.06 5.78003 5.8Z" fill="#292D32" />
                        <path d="M6.96027 18.5601H5.24023C4.83023 18.5601 4.49023 18.2201 4.49023 17.8101C4.49023 17.4001 4.83023 17.0601 5.24023 17.0601H6.96027C7.37027 17.0601 7.71027 17.4001 7.71027 17.8101C7.71027 18.2201 7.38027 18.5601 6.96027 18.5601Z" fill="#292D32" />
                        <path d="M12.5494 18.5601H9.10938C8.69938 18.5601 8.35938 18.2201 8.35938 17.8101C8.35938 17.4001 8.69938 17.0601 9.10938 17.0601H12.5494C12.9594 17.0601 13.2994 17.4001 13.2994 17.8101C13.2994 18.2201 12.9694 18.5601 12.5494 18.5601Z" fill="#292D32" />
                    </svg>
                </div>
            )
        },
        {
            id: 'cod',
            name: 'Cash On Delivery',
            icon: (
                <div className="flex items-center justify-center w-8 h-8">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17 21.25H7C3.35 21.25 1.25 19.15 1.25 15.5V8.5C1.25 4.85 3.35 2.75 7 2.75H17C20.65 2.75 22.75 4.85 22.75 8.5V15.5C22.75 19.15 20.65 21.25 17 21.25ZM7 4.25C4.14 4.25 2.75 5.64 2.75 8.5V15.5C2.75 18.36 4.14 19.75 7 19.75H17C19.86 19.75 21.25 18.36 21.25 15.5V8.5C21.25 5.64 19.86 4.25 17 4.25H7Z" fill="#292D32" />
                        <path d="M12 15.75C9.93 15.75 8.25 14.07 8.25 12C8.25 9.93 9.93 8.25 12 8.25C14.07 8.25 15.75 9.93 15.75 12C15.75 14.07 14.07 15.75 12 15.75ZM12 9.75C10.76 9.75 9.75 10.76 9.75 12C9.75 13.24 10.76 14.25 12 14.25C13.24 14.25 14.25 13.24 14.25 12C14.25 10.76 13.24 9.75 12 9.75Z" fill="#292D32" />
                        <path d="M5.5 15.25C5.09 15.25 4.75 14.91 4.75 14.5V9.5C4.75 9.09 5.09 8.75 5.5 8.75C5.91 8.75 6.25 9.09 6.25 9.5V14.5C6.25 14.91 5.91 15.25 5.5 15.25Z" fill="#292D32" />
                        <path d="M18.5 15.25C18.09 15.25 17.75 14.91 17.75 14.5V9.5C17.75 9.09 18.09 8.75 18.5 8.75C18.91 8.75 19.25 9.09 19.25 9.5V14.5C19.25 14.91 18.91 15.25 18.5 15.25Z" fill="#292D32" />
                    </svg>
                </div>
            )
        }
    ];

    const handleFormSubmit = () => {
        // Update the parent component's state with current form values
        handleSubmit();
    };

    return (
        <div className="w-full max-w-xl">
            {orderTypes !== 'Pickup' && (
                <>
                    {/* Delivery Address */}
                    <div className="mb-4">
                        <h1 className="block text-base font-bold mb-2">Delivery Address</h1>
                        <div
                            className="w-full min-h-[100px] relative bg-white p-3 border rounded-xl border-gray-300 text-gray-700 flex justify-start items-start gap-3 cursor-pointer hover:border-orange-500 transition-colors"
                            onClick={() => setIsAddressPopupOpen(true)}
                            data-delivery-address
                        >
                            <MapPin className="w-5 h-6 text-gray-900 flex-shrink-0 mt-1" />
                            <div className="flex-1">
                                {deliveryAddress ? (
                                    <p className='text-base font-medium text-gray-900'>{deliveryAddress}</p>
                                ) : (
                                    <p className='text-base font-medium text-gray-500'>Click to add delivery address</p>
                                )}
                                <p className='text-sm text-gray-500 mt-1'>Click to {deliveryAddress ? 'update' : 'add'} delivery address</p>
                            </div>
                        </div>
                    </div>

                    {/* <h2 className="font-semibold text-md">Personal Details</h2>
                    <div className="bg-white rounded-2xl p-4 pt-2 mt-2 w-full relative flex gap-2">
                        {isEditing ? (
                            <div className="space-y-3 w-full">
                              
                                <div className="flex space-x-2">
                                    <input
                                        type="text"
                                        value={firstName}
                                        onChange={(e) => setFirstName(e.target.value)}
                                        placeholder="First Name"
                                        className="w-1/2 p-2 border border-gray-300 rounded-md"
                                    />
                                    <input
                                        type="text"
                                        value={lastName}
                                        onChange={(e) => setLastName(e.target.value)}
                                        placeholder="Last Name"
                                        className="w-1/2 p-2 border border-gray-300 rounded-md"
                                    />
                                </div>

                              
                                <input
                                    type="tel"
                                    value={mobileNumber}
                                    onChange={(e) => setMobileNumber(e.target.value)}
                                    placeholder="Mobile Number"
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />

                               
                                <input
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    placeholder="Email"
                                    className="w-full p-2 border border-gray-300 rounded-md"
                                />
                            </div>
                        ) : (
                            <div className="text-base font-medium text-gray-800 space-y-1 leading-6 w-full">
                                <p>{firstName} {lastName}</p>
                                <p>{mobileNumber}</p>
                                <p>{email}</p>
                            </div>
                        )}
                        <div className="flex justify-start items-start w-fit ">
                            <button
                                onClick={() => setIsEditing(!isEditing)}
                                className="text-blue-600 text-base cursor-pointer font-medium hover:underline"
                            >
                                {isEditing ? 'Save' : 'Edit'}
                            </button>
                        </div>
                    </div>  */}
                </>
            )}

            {/* Voucher Section */}
            <div className="mt-6 border-gray-100">
                <h2 className="text-md font-semibold text-gray-900 mb-4">Voucher</h2>
                <div className="flex gap-2">
                    <input
                        type="text"
                        value={voucherCode}
                        onChange={(e) => setVoucherCode(e.target.value)}
                        placeholder="Enter Promo Code"
                        className=" w-[100%] p-3 rounded-lg bg-white"
                    />
                    <button
                        onClick={applyVoucher}
                        className="px-6 py-3 bg-orange-500 text-white rounded-lg font-medium hover:bg-orange-600 transition-colors whitespace-nowrap"
                    >
                        Apply to Order
                    </button>
                </div>
            </div>

            {/* Loyalty Points Section */}
            <div className="mt-6 border-gray-100">
                <h2 className="text-md font-semibold text-gray-900 mb-4">Loyalty Points Available</h2>
                <div className="flex items-center justify-between gap-2">
                    <div className=' flex justify-between items-center p-3 rounded-lg bg-white w-full'>
                        <p className="text-2xl font-bold text-gray-900">{loyaltyPoints.toLocaleString()}</p>
                        <p className="text-sm text-primary">${loyaltyValue}</p>
                    </div>
                    <button
                        onClick={applyLoyaltyPoints}
                        className="px-6 py-3 bg-orange-500 text-white rounded-lg font-medium hover:bg-orange-600 transition-colors whitespace-nowrap"
                    >
                        Apply to Order
                    </button>
                </div>
            </div>

            {/* Payment Section */}
            <div className="mb-4 mt-6">
                <h2 className={`text-xl font-bold mb-6 ${orderTypes === 'Pickup' ? 'block' : 'hidden'}`}>Payment Details</h2>
                <h1 className="block text-base font-bold mb-2">Payment Method</h1>

                <div className="space-y-2">
                    {paymentMethods.map((method) => (
                        <label key={method.id} className="flex items-center cursor-pointer p-3 rounded-4xl bg-white">
                            <div className="relative flex items-center">
                                <input
                                    type="radio"
                                    name="payment-method"
                                    value={method.id}
                                    checked={selectedMethod === method.id}
                                    onChange={() => setSelectedMethod(method.id)}
                                    className="appearance-none h-5 w-5 border border-gray-300 rounded-full checked:border-[6px] checked:border-gray-500"
                                />
                            </div>
                            <div className="ml-4 flex items-center">
                                {method.icon}
                                <span className="ml-3 text-md">{method.name}</span>
                                {method.id !== 'cod' && method.id !== 'creditcard' && (
                                    <span className="ml-2 text-sm text-gray-400">(Coming Soon)</span>
                                )}
                            </div>
                        </label>
                    ))}

                    {/* Credit Card Form */}
                    {selectedMethod === 'creditcard' && (
                        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                            {isLoadingStripeAccount ? (
                                <div className="flex items-center justify-center py-8">
                                    <div className="w-6 h-6 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></div>
                                    <span className="ml-2 text-gray-600">Loading payment setup...</span>
                                </div>
                            ) : stripeAccountId ? (
                                <Elements stripe={stripePromise}>
                                    <CreditCardForm
                                        onPaymentSuccess={(paymentIntentId, chargeId) => {
                                            console.log('Payment successful:', { paymentIntentId, chargeId });
                                            if (onPaymentSuccess) {
                                                onPaymentSuccess(paymentIntentId, chargeId);
                                            }
                                        }}
                                        onPaymentError={(error) => {
                                            console.error('Payment error:', error);
                                        }}
                                        amount={Math.round(cartTotal * 100)} // Convert to cents
                                        currency="usd"
                                        stripeAccount={stripeAccountId} // Dynamic stripe account ID
                                        applicationFeeAmount={3000} // $30 fee in cents
                                        description="pos order charge"
                                        isLoading={isOrderLoading}
                                        setIsLoading={(loading) => {
                                            // This will be handled by the parent component
                                            console.log('Credit card loading state:', loading);
                                        }}
                                    />
                                </Elements>
                            ) : (
                                <div className="text-center py-8">
                                    <p className="text-red-600 mb-4">Payment processing is not available for this restaurant.</p>
                                    <p className="text-gray-600 text-sm">Please contact the restaurant or try a different payment method.</p>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Only show the order button for COD, credit card has its own payment button */}
                    {selectedMethod !== 'creditcard' && (
                        <button
                            onClick={handleFormSubmit}
                            disabled={isOrderLoading}
                            className={`w-full py-4 mt-6 rounded-2xl transition-all duration-200 font-semibold text-lg ${isOrderLoading
                                ? 'bg-gray-400 text-gray-200 cursor-not-allowed'
                                : 'bg-primary text-white hover:bg-orange-600 hover:shadow-lg transform hover:scale-[1.02] cursor-pointer'
                                }`}
                        >
                            {isOrderLoading ? (
                                <div className="flex items-center justify-center space-x-2">
                                    <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                    <span>Processing Order...</span>
                                </div>
                            ) : (
                                orderTypes === 'Pickup' ? 'Complete Order' : 'Save and Complete Order'
                            )}
                        </button>
                    )}
                </div>
            </div>

            {/* Delivery Address Popup */}
            <DeliveryAddressPopup
                isOpen={isAddressPopupOpen}
                onClose={() => setIsAddressPopupOpen(false)}
                currentCustomer={currentCustomer}
                onAddressUpdate={(address) => setDeliveryAddress(address)}
            />
        </div>
    );
};

export default CheckoutForm;