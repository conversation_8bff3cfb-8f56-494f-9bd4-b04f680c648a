/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useRef, useState, useMemo, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { ChevronRight, ChevronLeft, Search } from "lucide-react";
import cowboysteak from "../../assets/cowboySteakhouse.jpg";
import ReviewModal from "../../components/Modal/ReviewModal";
import { assets } from "../../assets/assets";
import InfoModal from "../../components/Modal/InfoModal";
import GalleryModal from "../../components/Modal/GalleryModal";
import { useSelector } from 'react-redux';
import {
  getProductsWithParentCategory,
  getProductById,
  selectProducts,
  selectProductLoading,
  selectProductError,
} from '../../redux-store/slices/productSlice';
import MenuItems from "../../components/ProductPageComponent/MenuItems";
import TopSection from "../../components/ProductPageComponent/TopSection";
import type { RootState } from '../../redux-store/store';
// Import Redux cart functionality
import { useAppSelector, useAppDispatch } from "../../redux-store/hooks";
import YourItems from "../../components/ProductPageComponent/YourItems";
import { addFavorite, getDeviceById, selectCurrentDevice } from "../../redux-store/slices/deviceSlice";
// import { useAppDispatch } from "../../redux-store/hooks";
import {
  selectFormattedAddress,
  selectDeliveryOption
} from "../../redux-store/slices/addressSlice";
import Breadcrumb from "../../components/Breadcrumb";

// Types
interface Restaurant {
  id: string;
  name: string;
  cuisine: string;
  parentCategory?: string;
  rating: number;
  image: string;
  deal?: string;
  pizzaOffer?: string;
  priceOff?: string;
  price: number;
  discountPrice?: number;
  active: boolean;
  userId: any;
  reviews?: any[];
  hasReviews?: boolean;
  categoryId?: any[];
}

interface GalleryItem {
  src: string;
  type: "image" | "video";
  thumbnail?: string;
}

const RestaurantOrderPage = () => {
  const { id: restaurantId, userid: userId } = useParams<{
    id: string;
    userid: string;
  }>();

  // Store userid in localStorage when component mounts or userid changes
  useEffect(() => {
    if (userId) {
      localStorage.setItem('userid', userId);
      console.log('Stored userid in localStorage:', userId);
    }
  }, [userId]);

  const dispatch = useAppDispatch();

  // Redux selectors
  const currentDevice = useSelector(selectCurrentDevice);
  const products = useSelector(selectProducts);
  const loading = useSelector(selectProductLoading);
  const error = useSelector(selectProductError);

  // Local state
  const [activeCategory, setActiveCategory] = useState("All");
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const [showSearchMobile, setShowSearchMobile] = useState(false);
  const [isInfoModalOpen, setIsInfoModalOpen] = useState(false);
  const [isGalleryModalOpen, setIsGalleryModalOpen] = useState(false);
  const [currentGalleryIndex, setCurrentGalleryIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [deliveryMethod, setDeliveryMethod] = useState("Delivery");
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const [selectedAddress, setSelectedAddress] = useState<any>(null);
  const categoriesScrollRef = useRef<HTMLDivElement | null>(null);
  const currentCustomerId = useSelector((state: RootState) => state.customer?.currentCustomer?._id);
  const formattedAddress = useAppSelector(selectFormattedAddress);
  const deliveryOption = useAppSelector(selectDeliveryOption);
  const navigate = useNavigate();

  // Window resize handler
  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isMobile = windowWidth < 768;

  // Restaurant data derived from currentdevice
  const restaurantData = useMemo(() => {
    const calculateRating = (reviews: any[]): number => {
      if (!reviews || reviews.length === 0) {
        return 0;
      }
      const totalRating = reviews.reduce((sum, review) => {
        const avgRating = (review.food + review.service + review.ambiance) / 3;
        return sum + avgRating;
      }, 0);
      return totalRating / reviews.length;
    };

    const reviews = currentDevice?.reviews || [];
    return {
      _id: currentDevice?._id,
      userId: currentDevice?.userId,
      name: currentDevice?.name || "Loading...",
      description: currentDevice?.businessType || "Loading restaurant information...",
      image: currentDevice?.image || 'https://patronpal.com/assets/aaa.jpeg',
      City: currentDevice?.City,
      Country: currentDevice?.Country,
      Line1: currentDevice?.Line1,
      Line2: currentDevice?.Line2,
      delivery: currentDevice?.delivery !== undefined ? String(currentDevice.delivery) : undefined,
      rating: calculateRating(reviews),
      reviewCount: reviews.length || 0,
      hasReviews: currentDevice?.reviews && currentDevice?.reviews.length > 0,
      favorites: currentDevice?.favorites || [],
      address: selectedAddress ?
        `${selectedAddress.Line1 || ''}, ${selectedAddress.City || ''}, ${selectedAddress.State || ''}`.replace(/^,\s*|,\s*$/g, '') :
        `${currentDevice?.Line1 || ''}, ${currentDevice?.City || ''}, ${currentDevice?.State || ''}`.replace(/^,\s*|,\s*$/g, '') || "Loading address..."
    };
  }, [currentDevice, selectedAddress]);

  // Add useEffect to fetch address from localStorage
  useEffect(() => {
    const savedAddress = localStorage.getItem('lastSelectedAddress');
    if (savedAddress) {
      try {
        setSelectedAddress(JSON.parse(savedAddress));
      } catch (error) {
        console.error('Error parsing saved address:', error);
      }
    }
  }, []);

  // Gallery images
  const galleryMediaItems: GalleryItem[] = useMemo(() => [
    { src: assets.pallel, type: "video" },
    { src: cowboysteak, type: "image" },
    { src: cowboysteak, type: "image" },
  ], []);

  // Updated transformProductToRestaurant function to handle categoryParents
  const transformProductToRestaurant = useCallback((product: any): Restaurant => {
    // Calculate rating only if reviews exist
    const calculateRating = (reviews: any[]): number => {
      if (!reviews || reviews.length === 0) {
        return 0; // Return 0 for products without reviews instead of random rating
      }

      const totalRating = reviews.reduce((sum, review) => sum + (review.rating || 0), 0);
      return totalRating / reviews.length;
    };

    // Get parent category name from categoryParents
    const parentCategory = product.categoryParents && product.categoryParents.length > 0
      ? product.categoryParents[0].name
      : undefined;

    // Get cuisine from categoryId (subcategory)
    const cuisine = product.categoryId && product.categoryId.length > 0
      ? product.categoryId[0].name
      : undefined;

    return {
      userId: product.userId ?? userId,
      id: product._id,
      name: product.name,
      cuisine: cuisine,
      parentCategory: parentCategory,
      rating: calculateRating(product.reviewId),
      image: product.Product_pic,
      deal: product.discountPrice
        ? `${Math.round(((product.price - product.discountPrice) / product.price) * 100)}% OFF`
        : undefined,
      pizzaOffer: product.shortDescription,
      priceOff: product.retailPrice,
      price: product.price,
      discountPrice: product.discountPrice,
      active: product.active === "true",
      // Add review information to the restaurant object
      reviews: product.reviewId || [], // Include actual reviews
      hasReviews: product.reviewId && product.reviewId.length > 0,
    };
  }, [userId]);


  // Updated cuisineCategories to work with the actual object structure
  const cuisineCategories = useMemo(() => {
    const parentMap = new Map<string, any>();
    const activeProducts = products?.filter(product => product.active) || []; // Changed filter

    activeProducts.forEach(product => {
      // Check if product has categoryParents (from your JSON structure)
      if (product.categoryParents?.length > 0) {
        product.categoryParents.forEach((parent: any) => {
          if (!parentMap.has(parent._id)) {
            parentMap.set(parent._id, {
              name: parent.name,
              image: parent.category_pic || 'https://patronpal.com/assets/aaa.jpeg',
              categoryId: parent._id,
              children: []
            });
          }

          // Add the product's categories as children of this parent
          const parentEntry = parentMap.get(parent._id);
          if (product.categoryId?.length > 0) {
            product.categoryId.forEach((category: any) => {
              if (category.active === "true") {
                const alreadyExists = parentEntry?.children?.some((c: any) => c.categoryId === category._id);
                if (!alreadyExists) {
                  parentEntry.children.push({
                    name: category.name,
                    image: category.category_pic || 'https://patronpal.com/assets/aaa.jpeg',
                    categoryId: category._id
                  });
                }
              }
            });
          }
        });
      } else {
        // If no categoryParents, treat categoryId as top-level categories
        if (product.categoryId?.length > 0) {
          product.categoryId.forEach((category: any) => {
            if (category.active === "true") {
              if (!parentMap.has(category._id)) {
                parentMap.set(category._id, {
                  name: category.name,
                  image: category.category_pic || 'https://patronpal.com/assets/aaa.jpeg',
                  categoryId: category._id,
                  children: []
                });
              }
            }
          });
        }
      }
    });

    return Array.from(parentMap.values());
  }, [products]);

  // Updated getFilteredProducts to work with categoryParents
  const getFilteredProducts = useMemo(() => {
    if (activeCategory === "All") {
      return products?.filter(product => product.active) || []; // Show all active products
    }

    // Find the selected parent category
    const selectedParent = cuisineCategories.find(parent => parent.name === activeCategory);

    if (!selectedParent) {
      return [];
    }

    // Filter products that belong to this parent category
    return products?.filter(product => {
      // if (product.active !== "true") return false;

      // Check if product has this parent in categoryParents
      const hasParent = product.categoryParents?.some((parent: any) => parent._id === selectedParent.categoryId);

      // If no categoryParents, check if the product's categoryId matches
      const hasDirectCategory = !product.categoryParents?.length &&
        product.categoryId?.some((category: any) => category._id === selectedParent.categoryId);

      return hasParent || hasDirectCategory;
    }) || [];
  }, [products, activeCategory, cuisineCategories]);

  const groupedProductsBySubcategory = useMemo(() => {
    if (activeCategory === "All") {
      const allGrouped: { [key: string]: Restaurant[] } = {};

      const activeProducts = products?.filter(product => product.active) || [];

      activeProducts?.forEach(product => {
        // Handle products with categoryId
        if (product.categoryId && product.categoryId.length > 0) {
          product.categoryId?.forEach((category: any) => {
            if (category.active === "true") {
              if (!allGrouped[category.name]) {
                allGrouped[category.name] = [];
              }

              const productExists = allGrouped[category.name].some(p => p.id === product._id);
              if (!productExists) {
                allGrouped[category.name].push(transformProductToRestaurant(product));
              }
            }
          });
        } else {
          // Handle products without categories - group them under "Uncategorized"
          if (!allGrouped["Uncategorized"]) {
            allGrouped["Uncategorized"] = [];
          }

          const productExists = allGrouped["Uncategorized"].some(p => p.id === product._id);
          if (!productExists) {
            allGrouped["Uncategorized"].push(transformProductToRestaurant(product));
          }
        }
      });

      return allGrouped;
    }

    // Rest of the function remains the same...
    const selectedParent = cuisineCategories.find(parent => parent.name === activeCategory);
    if (!selectedParent) {
      return {};
    }

    const grouped: { [key: string]: Restaurant[] } = {};

    const filteredProducts = getFilteredProducts;

    filteredProducts.forEach(product => {
      product.categoryId?.forEach((category: any) => {
        if (category.active === "true") {
          if (!grouped[category.name]) {
            grouped[category.name] = [];
          }

          const productExists = grouped[category.name].some(p => p.id === product._id);
          if (!productExists) {
            grouped[category.name].push(transformProductToRestaurant(product));
          }
        }
      });
    });

    return grouped;
  }, [getFilteredProducts, activeCategory, cuisineCategories, transformProductToRestaurant, products]);


  // Filtered restaurants by search

  // Event handlers
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const scrollSection = useCallback((direction: 'left' | 'right') => {
    if (categoriesScrollRef.current) {
      const scrollAmount = direction === 'right' ? 300 : -300;
      categoriesScrollRef.current.scrollBy({
        left: scrollAmount,
        behavior: 'smooth'
      });
    }
  }, []);

  // Effects
  useEffect(() => {
    if (restaurantId) {
      dispatch(getDeviceById(restaurantId) as any);
    }

    // Always fetch products when restaurantId or userId changes
    if (userId) {
      dispatch(getProductsWithParentCategory({ userId: userId }));
    }
  }, [dispatch, restaurantId, userId]);

  // Add favorite toggle handler
  const handleFavoriteToggle = (restaurantId: string) => {


    if (!currentCustomerId) {
      console.log('[ProductPage] No userId, redirecting to login');
      navigate('/login');
      
      return;
    }

    // console.log('[ProductPage] Dispatching addFavorite with:', { deviceId: restaurantId, customerId: currentCustomerId });
    dispatch(addFavorite({
      deviceId: restaurantId,
      customerId: currentCustomerId
    }) as any);
  };

  // Add useEffect to log favorites data whenever it changes
  useEffect(() => {
    if (currentDevice) {
      // console.log('[ProductPage] handleFavoriteToggle called with:', { userId });

      // Check if user has favorited this restaurant
      const isFavorited = currentDevice.favorites?.some(fav => {
        const favCustomerId = typeof fav.customerId === 'object' ? fav.customerId?._id : fav.customerId;
        const result = favCustomerId === currentCustomerId;
        // console.log('[ProductPage] Favorite check:', { favCustomerId, userId, result });
        return result;
      });

      console.log('[ProductPage] Is restaurant favorited:', isFavorited);
    }
  }, [currentDevice, currentCustomerId]);

  // Add this useEffect near the other useEffect hooks
  useEffect(() => {
    // Scroll to top when component mounts or when restaurantId changes
    window.scrollTo(0, 0);
  }, [restaurantId]);


  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading: {error}</p>
          <button
            onClick={() => restaurantId && dispatch(getProductById(restaurantId) as any)}
            className="bg-orange-500 text-white px-4 py-2 rounded-md hover:bg-orange-600"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 md:px-14 px-0">
      <main className="container mx-auto px-4 py-4 flex flex-col lg:flex-row gap-6">
        {/* Left side - Restaurant details and menu */}
        <div className="w-full lg:w-2/3 min-w-0">
          {/* Breadcrumb */}
          <Breadcrumb
            items={[
              { label: "Home", path: "/" },
              { label: formattedAddress || "All Restaurants", path: `/all-restaurants?type=${deliveryOption.toLowerCase()}${formattedAddress ? `&address=${encodeURIComponent(formattedAddress)}` : ''}` },
              { label: restaurantData.name, path: `#` }
            ]}
          />

          {/* Top Section */}
          <TopSection
            restaurantData={restaurantData as any}
            setIsReviewModalOpen={setIsReviewModalOpen}
            setIsInfoModalOpen={setIsInfoModalOpen}
            galleryMediaItems={galleryMediaItems}
            setCurrentGalleryIndex={setCurrentGalleryIndex}
            setIsGalleryModalOpen={setIsGalleryModalOpen}
            onFavoriteToggle={handleFavoriteToggle}
            userId={currentCustomerId}
          />

          {/* Search and Categories */}
          <div className="mb-6 m-1.5 py-3 flex items-center space-x-5 mt-5 sticky top-20 z-10 bg-gray-50">
            <div className="relative max-w-md  md:w-2/4 ">
              {isMobile ? (
                <div className="flex items-center space-x-3 ">
                  <div className="p-3 w-12 text-gray-600 rounded-full shadow-md shadow-gray-500">
                    <Search onClick={() => setShowSearchMobile(!showSearchMobile)} size={20} />
                  </div>
                  {showSearchMobile && (
                    <input
                      type="text"
                      placeholder="Search Menu..."
                      value={searchQuery}
                      onChange={handleSearchChange}
                      className="w-full py-2 bg-white rounded-md px-3 text-gray-700 outline-none border border-orange-500 rounded-lg"
                    />
                  )}
                </div>
              ) : (
                <div className="flex items-center bg-white lg:w-[350px] md:w-[200px] w-[100px] border border-orange-500 rounded-lg">
                  <div className="pl-3 text-gray-600">
                    <Search size={18} />
                  </div>
                  <input
                    type="text"
                    placeholder="Search Menu..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="w-full py-2 px-3 text-gray-700 bg-transparent outline-none"
                  />
                </div>
              )}
            </div>

            {/* Categories */}
            {!showSearchMobile && (
              <>
                <div
                  ref={categoriesScrollRef}
                  className="overflow-x-auto scrollbar-hide md:w-2/4 relative pr-9"
                  style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
                >
                  <div className="flex gap-2 overflow-x-auto  space-x-2 w-min">
                    {/* All category button */}
                    <button
                      onClick={() => setActiveCategory("All")}
                      className={`px-4 py-1 rounded-full whitespace-nowrap ${activeCategory === "All"
                        ? "bg-orange-50 border border-primary text-primary"
                        : "bg-white border border-gray-300 text-gray-700"
                        }`}
                    >
                      All
                    </button>

                    
                    {cuisineCategories.map((parent) => (
                      <button
                        key={parent.categoryId}
                        onClick={() => setActiveCategory(parent.name)}
                        className={`px-4 py-1 rounded-full whitespace-nowrap flex items-center gap-1 ${activeCategory === parent.name
                          ? "bg-orange-50 border border-primary text-primary"
                          : "bg-white border border-gray-300 text-gray-700"
                          }`}
                      >
                        {parent.name}
                      </button>
                    ))}
                  </div>
                </div>
             
                <button
                  className="absolute lg:left-[350px] md:left-[250px] left-[45px] top-8 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-sm shadow-gray-400 hover:bg-gray-100 z-10"
                  onClick={() => scrollSection('left')}
                >
                  <ChevronLeft className="w-8 h-8 text-primary" />
                </button>
               
                <button
                  className="absolute right-1 top-7 transform -translate-y-1/2 bg-white rounded-full p-1 shadow-sm shadow-gray-400 hover:bg-gray-100 z-10"
                  onClick={() => scrollSection('right')}
                >
                  <ChevronRight className="w-8 h-8 text-primary" />
                </button>
              </>
            )}
          </div>
          {/* Menu Items */}
          <MenuItems
            restaurants={getFilteredProducts.map(transformProductToRestaurant)}
            restaurantsByCategories={groupedProductsBySubcategory}
            userId={userId || ''}
            searchQuery={searchQuery}
            isLoading={loading}
            restaurantData={restaurantData as any} // Pass restaurant data from TopSection
          />
        </div>

        {/* Right side - Order summary */}
        <div className="w-full flex flex-col lg:w-1/3 bg-white rounded-lg shadow-md p-2 sticky top-24 mt-12 z-0 max-h-[calc(100vh-7rem)] overflow-hidden">
          {/* Cart items - remove cartItems and setCartItems props */}
          <YourItems
            deliveryMethod={deliveryMethod}
            setDeliveryMethod={setDeliveryMethod}
          />
        </div>
      </main>

      {/* Modals */}
      <ReviewModal
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        restaurantId={restaurantId || ''}
        restaurantName={restaurantData.name}
        rating={restaurantData.rating}
        reviewCount={restaurantData.reviewCount}
        restaurantImage={restaurantData.image}
        productData={currentDevice as any}// Pass the complete product data
      />

      <InfoModal
        isOpen={isInfoModalOpen}
        onClose={() => setIsInfoModalOpen(false)}
        restaurantData={{
          name: currentDevice?.name || restaurantData.name,
          address: restaurantData.address,
          City: currentDevice?.City,
          State: currentDevice?.State,
          Country: currentDevice?.Country,
          Line1: currentDevice?.Line1,
          Line2: currentDevice?.Line2,
          Phoneno: currentDevice?.Phoneno,
          pickupStartTime: currentDevice?.pickupStartTime,
          pickupEndTime: currentDevice?.pickupEndTime,
          deliveryStartTime: currentDevice?.deliveryStartTime,
          deliveryEndTime: currentDevice?.deliveryEndTime,
          delivery: currentDevice?.delivery !== undefined ? String(currentDevice.delivery) : undefined,
          ChargesFreeKm: currentDevice?.ChargesFreeKm,
          ChargesperKm: currentDevice?.ChargesperKm
        }}
      />
      <GalleryModal
        isOpen={isGalleryModalOpen}
        onClose={() => setIsGalleryModalOpen(false)}
        images={galleryMediaItems}
        initialIndex={currentGalleryIndex}
      />
    </div>
  );
};

export default RestaurantOrderPage;
